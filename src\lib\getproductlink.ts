import { getIsAdmin } from '@/stores/admin'
import type { CategoryProduct } from '@/types/GetCategoryProduct'

export const getProductLink = ({ product, productUrlPrefix }: { product: CategoryProduct; productUrlPrefix?: string }) => {
  const isAdmin = getIsAdmin()

  if (isAdmin) {
    return `/catalog/products/${product.prod_id}`
  }

  const defaultPrefix = product.isVirtual ? '/catalog/item' : '/catalog/products'
  const prefix = productUrlPrefix || defaultPrefix

  if (product.isVirtual) {
    return `${prefix}/${product.id}?sku=${encodeURIComponent(product.prod_analogsku)}`
  }

  return `${prefix}/${product.prod_id}?sku=${encodeURIComponent(product.prod_sku)}`
}
