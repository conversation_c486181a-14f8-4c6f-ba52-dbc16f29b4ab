---
import Layout from '@/layouts/Layout.astro'
import { trpc } from '@/trpc'

import { updateURLParams } from '@/stores/qs'
import ProductFilters from '@components/ProductFilters.astro'
import { pluralizeProducts } from '@/lib/pluralizeProducts'
import { getInitialFilterValue, getPageParams } from '@/lib/pageUtils'
import Categories from '@components/Categories.astro'
import BreadcrumbsNav from '@components/react/Breadcrumbs'
import FilterSkeleton from '@components/FilterSkeleton.astro'
import ProductViewSwitcher from '@components/ProductViewSwitcher.astro'
import ProductPaginator from '@components/ProductPaginator.astro'
import { ProductViewer } from '@components/react/ProductViewer'
import { getBaseUrl, COMPANY_NAME, getRtiImageUrl } from '@/lib/config'
import { CSorting } from '@/components/react/CSorting'
import { HtmlEditorButton } from '@components/react/HtmlEditorButton'

type ProductsDataType = Awaited<ReturnType<typeof trpc.products.getCategoryProduct.query>>

const searchParams = Astro.url.searchParams

// Разделяем путь на части
const category = Astro.params['category']?.split('/') || []
// Проверяем, является ли последняя часть числом (страницей)
const lastPartIsNumber = !isNaN(Number(category[category.length - 1]))
// Если последняя часть - число, то берем предпоследнюю часть как categoryId
// Иначе берем последнюю часть как categoryId
const categoryId = lastPartIsNumber ? category[category.length - 2] : category[category.length - 1] || '1'

if (!categoryId) {
  return Astro.redirect('/')
}

// Получаем строку query-параметров
const queryString = Astro.url.search

// Установка заголовков кеширования с учетом query-параметров
Astro.response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=3600')
// Добавляем заголовок Vary для правильного кеширования разных вариантов страницы
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

// Можно также добавить ETag или другой идентификатор для более точного кеширования
// Например, можно использовать хеш от комбинации categoryId и queryString
const cacheKey = `${categoryId}${queryString}`
Astro.response.headers.set('ETag', `"${cacheKey}"`)

const currentViewMode = Astro.locals.viewMode
const isMobile = Astro.locals.isMobile
const isAdmin = Astro.locals.isAdmin

const { limit, page, order, sorting, filters, search } = getPageParams(searchParams)
const { filtersValue, filtersStr } = getInitialFilterValue(searchParams)

let productsData: ProductsDataType | null = null

let htmlHeader: { body: string } | null = null
let htmlBody: { body: string } | null = null

const categories = await trpc.products.getSubCategories.query(categoryId)
if (!categories) {
  // productsData = await trpc.products.getCategoryProduct.query({
  productsData = await trpc.products.getCategoryProductMeili.query({
    identifier: categoryId,
    limit: isMobile ? 20 : Math.min(Number(limit), 100),
    page,
    order,
    sorting,
    filters
  })
}

try {
  const [header, body] = await Promise.all([
    trpc.services.getHtmkChunk.query({ key: `${String(categoryId)}_header` }),
    trpc.services.getHtmkChunk.query({ key: `${String(categoryId)}_body` })
  ])

  htmlHeader = header
  htmlBody = body
} catch (error) {
  console.log(error)
}

const productTotalCount = productsData?.products?.meta?.totalCount || 0

updateURLParams(
  {
    filters,
    page: page ? Number(page) : undefined,
    sorting,
    search
  },
  undefined,
  false
)

// Формируем SEO-данные
const currentUrl = new URL(Astro.url.pathname, Astro.url.href).toString()
const canonicalUrl = new URL(Astro.url.pathname.split('/page/')[0], Astro.url.href).toString()

// Убедимся, что URL абсолютный для микроразметки
const absoluteUrl = currentUrl.startsWith('http') ? currentUrl : `${getBaseUrl(Astro.url)}${Astro.url.pathname}`
const selfUrl = new URL(Astro.url).toString()

// Получаем URL для пагинации
const getPageUrl = (pageNum: number) => {
  const url = new URL(Astro.url)
  url.searchParams.set('page', pageNum.toString())
  return url.toString()
}

let catNames = ''

try {
  catNames = categories?.map((i: { cat_title: string }) => i.cat_title).join(', ') || ''
} catch (error) {}

// Формируем уникальный заголовок с учетом номера страницы
const currentPage = page || 1
const pageInfo = currentPage > 1 ? ` - Страница ${currentPage}` : ''
const categoryTitle = productsData?.category?.cat_title
  ? `${productsData.category.cat_title}${pageInfo} купить оптом и в розницу`
  : `Каталог ${catNames} ${pageInfo}`

// Добавляем информацию о номере страницы для большей уникальности
const pageDescriptionSuffix = currentPage > 1 ? ` Страница ${currentPage} из ${productsData?.products?.meta?.pageCount || 1}.` : ''

const categoryDescription = productsData?.category?.cat_note
  ? `${productsData.category.cat_note}. В наличии ${productTotalCount || 'тысячи'} ${pluralizeProducts(productTotalCount)}.${pageDescriptionSuffix} Доставка по России.`
  : `${catNames} - Купить оптом и в розницу с доставкой по России.`

// Используем правильный путь к изображению категории
const categoryImage = productsData?.category?.cat_pic ? getRtiImageUrl(productsData.category.cat_pic) : getRtiImageUrl('og-image.jpg')

// Формируем ключевые слова
const keywords = [productsData?.category?.cat_title, 'купить', 'цена', 'оптом', 'розница', 'доставка', productsData?.category?.cat_keywords || 0]
  .filter(Boolean)
  .join(', ')

// Получаем базовый URL
const baseUrl = getBaseUrl(Astro.url)

// Каноникал и индексация для фильтров/сортировок/поиска/нетипичных параметров
const hasFilterParams = Boolean(filters) || Boolean(search) || typeof sorting !== 'undefined' || (order && order !== 'desc') || (limit && limit !== 50)
const canonicalHref = hasFilterParams ? canonicalUrl : currentPage > 1 ? selfUrl : absoluteUrl
const robotsMeta = hasFilterParams ? 'noindex, follow' : 'index, follow'

// Обновляем breadcrumbs с динамическим URL
const breadcrumbsItems = [
  {
    '@type': 'ListItem',
    'position': 1,
    'name': 'Главная',
    'item': baseUrl
  },
  {
    '@type': 'ListItem',
    'position': 2,
    'name': 'Каталог',
    'item': `${baseUrl}/catalog`
  },
  {
    '@type': 'ListItem',
    'position': 3,
    'name': productsData?.category?.cat_title || '',
    'item': canonicalUrl
  }
]

const CATEGORY_FOR_ACTIVE_FILTERS = [1, 5]
function checkActiveCatFilters(id: number) {
  return CATEGORY_FOR_ACTIVE_FILTERS.some((i) => i === id)
}
---

<Layout description={categoryDescription} title={categoryTitle}>
  <!-- Основные мета-теги -->
  <link rel='canonical' href={canonicalHref} />
  <meta name='description' content={categoryDescription} />
  <meta name='keywords' content={keywords} />
  <meta name='robots' content={robotsMeta} />

  <!-- Пагинация для SEO -->
  {currentPage > 1 && <link rel='prev' href={getPageUrl(currentPage - 1)} />}
  {currentPage < productsData?.products?.meta.pageCount && <link rel='next' href={getPageUrl(currentPage + 1)} />}

  <!-- Open Graph -->
  <meta property='og:type' content='website' />
  <meta property='og:url' content={canonicalHref} />
  <meta property='og:title' content={categoryTitle} />
  <meta property='og:description' content={categoryDescription} />
  <meta property='og:image' content={categoryImage} />
  <meta property='og:site_name' content={COMPANY_NAME} />

  <!-- Для мессенджеров -->
  <meta property='og:image:width' content='600' />
  <meta property='og:image:height' content='600' />
  <meta property='og:image:alt' content={categoryTitle} />

  <!-- Schema.org разметка -->
  <script
    is:inline
    type='application/ld+json'
    set:html={JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      'name': categoryTitle,
      'description': categoryDescription,
      'url': absoluteUrl,
      'mainEntity': {
        '@type': 'ItemList',
        'numberOfItems': productTotalCount,
        'itemListElement': []
      },
      'image': categoryImage,
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': breadcrumbsItems
      },
      'offers': {
        '@type': 'AggregateOffer',
        'offerCount': productTotalCount,
        'availability': 'https://schema.org/InStock'
      }
    })}
  />

  <BreadcrumbsNav server:defer prefix='catalog/' path={Astro.url.pathname} />
  {
    categories ? (
      <div class='mt-3'>
        <div class='container mx-auto flex justify-center'>
          {isAdmin && (
            <div class='mb-2'>
              <HtmlEditorButton size='sm' label=' ' chunkKey={`${String(categoryId)}_header`} client:only='react' />
            </div>
          )}
          <div set:html={htmlHeader?.body} />
        </div>
        <Categories categories={categories} />
        <div class='container mx-auto flex justify-center'>
          {isAdmin && (
            <div class='mb-2'>
              <HtmlEditorButton size='sm' label=' ' chunkKey={`${String(categoryId)}_body`} client:only='react' />
            </div>
          )}
          <div set:html={htmlBody?.body} />
        </div>
      </div>
    ) : (
      <div class='mt-3'>
        {/* Центрированный контейнер для фильтров и контента */}
        <div class='container mx-auto'>
          <div class='flex w-full xl:gap-10'>
            {/* Мобильные фильтры */}

            {/* Десктопные фильтры */}
            {!isMobile && checkActiveCatFilters(productsData?.category.cat_id) && (
              <div class='relative hidden w-1/4 max-w-48 md:min-w-36 xl:md:min-w-40 xl:block'>
                <ProductFilters
                  initialFilterValue={filtersValue}
                  categoryId={productsData?.category.cat_id}
                  filtersList={productsData?.category?.filters}
                  transition:persist
                  isMobile={false}
                  server:defer
                >
                  <div slot='fallback' class='relative -mt-4 min-w-36 max-w-48 space-y-3 p-2 xl:min-w-40'>
                    <div />
                    {productsData?.category?.filters
                      ?.filter((i: { title?: string }) => i.title)
                      .map((i: { title?: string }) => (
                        <FilterSkeleton title={i.title || ''} />
                      ))}
                  </div>
                </ProductFilters>
              </div>
            )}

            <div class='relative w-full shrink grow'>
          <div>
            {isAdmin && (
              <div class='mb-2'>
                <HtmlEditorButton size='sm' label=' ' chunkKey={`${String(categoryId)}_header`} client:only='react' />
              </div>
            )}
            <div set:html={htmlHeader?.body} />
          </div>
          <div class='mt-5 items-start justify-between md:mt-2 md:flex'>
            <div class='md:justify-left flex items-start justify-between gap-2 md:items-center md:gap-5'>
              {!htmlHeader?.body && <h1 class='text-sm font-bold sm:text-base md:text-xl'>{productsData?.category?.cat_title || ''}</h1>}
              <div>
                {filtersStr && <span class='text-default-600'>({filtersStr})</span>}
                <div class='text-sm text-default-600'>
                  {productTotalCount} {pluralizeProducts(productTotalCount)}
                  <div class='text-sm text-default-600'>
                    Страница {page} из {productsData?.products?.meta?.pageCount}
                  </div>
                </div>
              </div>
              {isMobile && (
                <div>
                  <div class='flex w-full justify-between gap-2 xl:hidden'>
                    <CSorting
                      client:only='react'
                      variant='bordered'
                      size='sm'
                      columns={productsData?.category?.columns || []}
                      initialSorting={sorting}
                      isMobile={true}
                    >
                      <div slot='fallback' class='h-10 w-24 animate-pulse rounded-lg bg-default-100 ease-in-out' />
                    </CSorting>

                    <ProductFilters
                      initialFilterValue={filtersValue}
                      categoryId={productsData?.category.cat_id}
                      filtersList={productsData?.category?.filters}
                      _filters={filters}
                      _search={search}
                      transition:persist
                      isMobile={true}
                    >
                      <div slot='fallback' class='flex justify-end gap-2'>
                        {productsData?.category?.filters
                          ?.filter((i: { title?: string }) => i.title)
                          .map((i: { title?: string }) => (
                            <div class='flex h-10 w-24 animate-pulse items-center justify-center rounded-md bg-default-200 text-center text-sm dark:bg-default-100'>
                              <div class=''>{i.title || ''}</div>
                            </div>
                          ))}
                      </div>
                    </ProductFilters>
                  </div>
                </div>
              )}
            </div>

            {!isMobile && (
              <div class='mb-2 flex items-center justify-between gap-2'>
                {currentViewMode == 'grid' && (
                  <div class='hidden sm:block'>
                    <CSorting
                      client:only='react'
                      variant='bordered'
                      size='md'
                      server:defer
                      label='Сортировать по'
                      columns={productsData?.category?.columns || []}
                      initialSorting={sorting}
                      isMobile={isMobile}
                    >
                      <div slot='fallback' class='h-10 w-24 animate-pulse rounded-lg bg-default-100 ease-in-out' />
                    </CSorting>
                  </div>
                )}
                <div class='flex justify-end'>
                  <ProductViewSwitcher server:defer />
                </div>
              </div>
            )}
          </div>
          <div class='mt-1 rounded-lg border dark:border-default-200 sm:p-2'>
            {/* <div class='container mx-auto flex justify-center'>
              <div set:html={htmlHeader?.body} />
            </div> */}
            <ProductViewer
              client:load
              data={productsData}
              categoryId={categoryId}
              limit={limit || 50}
              filters={filters}
              order={order}
              initialSorting={sorting}
              isMobile={isMobile}
              viewMode={currentViewMode}
              editorModeEnable={isAdmin}
            />
          </div>
          <ProductPaginator
            showPageInput
            showSelectLimit={true}
            isMobile={isMobile}
            transition:persist
            limit={limit}
            total={productsData?.products?.meta.pageCount}
            initPage={page}
          />
        </div>
          </div>
        </div>
        <div class='container mx-auto flex justify-center'>
          {isAdmin && (
            <div class='mb-2'>
              <HtmlEditorButton size='sm' label=' ' chunkKey={`${String(categoryId)}_body`} client:only='react' />
            </div>
          )}
          <div set:html={htmlBody?.body} />
        </div>
      </div>
    )
  }
</Layout>
